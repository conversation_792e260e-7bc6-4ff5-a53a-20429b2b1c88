<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?></title>
    <link rel="stylesheet" href="/static/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .cache-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #007cba;
        }
        
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007cba;
            margin-bottom: 5px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .cache-actions {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007cba;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .cache-status {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-enabled {
            color: #27ae60;
            font-weight: bold;
        }
        
        .status-disabled {
            color: #e74c3c;
            font-weight: bold;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        .message {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            display: none;
        }
        
        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <div class="admin-header">
            <h1><i class="fas fa-memory"></i> 缓存管理</h1>
            <p>管理系统缓存，提升网站性能</p>
        </div>

        <div id="message" class="message"></div>

        <!-- 缓存状态 -->
        <div class="cache-status">
            <h2><i class="fas fa-info-circle"></i> 缓存状态</h2>
            <p>缓存功能：
                <?php if ($cache_enabled): ?>
                    <span class="status-enabled"><i class="fas fa-check-circle"></i> 已启用</span>
                <?php else: ?>
                    <span class="status-disabled"><i class="fas fa-times-circle"></i> 已禁用</span>
                <?php endif; ?>
            </p>
            <p>缓存目录：<code><?= htmlspecialchars($stats['cache_dir']) ?></code></p>
        </div>

        <!-- 缓存统计 -->
        <div class="cache-stats">
            <div class="stat-card">
                <h3><i class="fas fa-file"></i> 缓存文件</h3>
                <div class="stat-value"><?= $stats['total_files'] ?></div>
                <div class="stat-label">总文件数</div>
            </div>
            
            <div class="stat-card">
                <h3><i class="fas fa-check"></i> 有效缓存</h3>
                <div class="stat-value"><?= $stats['valid_count'] ?></div>
                <div class="stat-label">未过期文件</div>
            </div>
            
            <div class="stat-card">
                <h3><i class="fas fa-clock"></i> 过期缓存</h3>
                <div class="stat-value"><?= $stats['expired_count'] ?></div>
                <div class="stat-label">已过期文件</div>
            </div>
            
            <div class="stat-card">
                <h3><i class="fas fa-memory"></i> 内存缓存</h3>
                <div class="stat-value"><?= $stats['memory_count'] ?></div>
                <div class="stat-label">内存中缓存</div>
            </div>
            
            <div class="stat-card">
                <h3><i class="fas fa-hdd"></i> 缓存大小</h3>
                <div class="stat-value"><?= formatBytes($stats['total_size']) ?></div>
                <div class="stat-label">磁盘占用</div>
            </div>
        </div>

        <!-- 缓存操作 -->
        <div class="cache-actions">
            <button class="btn btn-success" onclick="refreshStats()">
                <i class="fas fa-sync-alt"></i> 刷新统计
            </button>
            
            <button class="btn btn-primary" onclick="warmupCache()">
                <i class="fas fa-fire"></i> 预热缓存
            </button>
            
            <button class="btn btn-warning" onclick="cleanupCache()">
                <i class="fas fa-broom"></i> 清理过期
            </button>
            
            <button class="btn btn-danger" onclick="clearCache()">
                <i class="fas fa-trash"></i> 清空缓存
            </button>
        </div>
    </div>

    <script>
        // 显示消息
        function showMessage(text, type = 'success') {
            const messageEl = document.getElementById('message');
            messageEl.textContent = text;
            messageEl.className = `message ${type}`;
            messageEl.style.display = 'block';
            
            setTimeout(() => {
                messageEl.style.display = 'none';
            }, 5000);
        }

        // 刷新统计
        function refreshStats() {
            location.reload();
        }

        // 预热缓存
        function warmupCache() {
            if (!confirm('确定要预热缓存吗？这可能需要一些时间。')) {
                return;
            }
            
            fetch('?c=cache&action=warmup', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('操作失败：' + error.message, 'error');
            });
        }

        // 清理过期缓存
        function cleanupCache() {
            if (!confirm('确定要清理过期缓存吗？')) {
                return;
            }
            
            fetch('?c=cache&action=cleanup', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('操作失败：' + error.message, 'error');
            });
        }

        // 清空所有缓存
        function clearCache() {
            if (!confirm('确定要清空所有缓存吗？这将删除所有缓存文件。')) {
                return;
            }
            
            fetch('?c=cache&action=clear', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    setTimeout(() => location.reload(), 2000);
                } else {
                    showMessage(data.message, 'error');
                }
            })
            .catch(error => {
                showMessage('操作失败：' + error.message, 'error');
            });
        }
    </script>
</body>
</html>

<?php
// 格式化字节大小
function formatBytes($size, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}
?>
