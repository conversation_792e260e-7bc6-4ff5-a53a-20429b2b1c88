<?php
/**
 * TwoNav高级版 - 快速安装脚本
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('STATIC_PATH', ROOT_PATH . '/static');
define('DATA_PATH', ROOT_PATH . '/data');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 引入配置文件
require_once ROOT_PATH . '/config.php';

echo "<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>TwoNav高级版 - 快速安装</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; color: #333; }
        .step { margin: 20px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #007bff; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>🚀 TwoNav高级版 - 快速安装</h1>
            <p>一键安装，快速部署</p>
        </div>";

try {
    echo "<div class='step'>📋 <strong>步骤 1:</strong> 检查系统要求...</div>";
    
    // 检查PHP版本
    if (version_compare(PHP_VERSION, '7.4.0', '<')) {
        throw new Exception('PHP版本需要 >= 7.4，当前版本: ' . PHP_VERSION);
    }
    echo "<div class='step success'>✅ PHP版本检查通过: " . PHP_VERSION . "</div>";
    
    // 检查扩展
    $extensions = ['sqlite3', 'pdo', 'json'];
    foreach ($extensions as $ext) {
        if (!extension_loaded($ext)) {
            throw new Exception("缺少必需的PHP扩展: {$ext}");
        }
    }
    echo "<div class='step success'>✅ PHP扩展检查通过</div>";
    
    echo "<div class='step'>📁 <strong>步骤 2:</strong> 创建必要目录...</div>";
    
    // 创建目录
    $dirs = [DATA_PATH, DATA_PATH . '/uploads'];
    foreach ($dirs as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("无法创建目录: {$dir}");
            }
        }
    }
    echo "<div class='step success'>✅ 目录创建成功</div>";
    
    echo "<div class='step'>🗄️ <strong>步骤 3:</strong> 初始化数据库...</div>";
    
    // 执行安装
    $installer = new Core\Installer();
    $installer->install();
    
    echo "<div class='step success'>✅ 数据库初始化成功</div>";
    
    echo "<div class='step'>🎉 <strong>步骤 4:</strong> 安装完成！</div>";
    
    echo "<div class='step success'>
        <h3>🎊 安装成功！</h3>
        <p><strong>默认管理员账号信息：</strong></p>
        <pre>用户名: admin
密码: admin123</pre>
        <p><strong>重要提醒：</strong></p>
        <ul>
            <li>请立即登录管理后台修改默认密码</li>
            <li>建议删除此安装文件以提高安全性</li>
            <li>如需重新安装，请先删除 data/navstack.db 文件</li>
        </ul>
        <p><strong>快速访问链接：</strong></p>
        <a href='index.php' class='btn'>🏠 访问首页</a>
        <a href='index.php?c=admin' class='btn'>⚙️ 管理后台</a>
        <a href='demo.html' class='btn'>📖 项目演示</a>
    </div>";
    
} catch (Exception $e) {
    echo "<div class='step error'>
        <h3>❌ 安装失败</h3>
        <p><strong>错误信息：</strong> " . htmlspecialchars($e->getMessage()) . "</p>
        <p><strong>解决建议：</strong></p>
        <ul>
            <li>检查服务器是否满足系统要求</li>
            <li>确保目录权限正确（755）</li>
            <li>检查PHP扩展是否完整</li>
            <li>查看服务器错误日志获取详细信息</li>
        </ul>
        <a href='install.php' class='btn'>🔧 使用图形化安装</a>
    </div>";
}

echo "
        <div class='step info'>
            <h4>📚 系统信息</h4>
            <ul>
                <li><strong>项目名称：</strong>TwoNav高级版</li>
                <li><strong>版本：</strong>1.0.0</li>
                <li><strong>PHP版本：</strong>" . PHP_VERSION . "</li>
                <li><strong>数据库：</strong>SQLite</li>
                <li><strong>安装时间：</strong>" . date('Y-m-d H:i:s') . "</li>
            </ul>
        </div>
        
        <div class='step'>
            <h4>🔗 相关链接</h4>
            <ul>
                <li><a href='https://github.com/navstack/twonav-advanced' target='_blank'>GitHub项目地址</a></li>
                <li><a href='https://github.com/navstack/twonav-advanced/wiki' target='_blank'>使用文档</a></li>
                <li><a href='https://github.com/navstack/twonav-advanced/issues' target='_blank'>问题反馈</a></li>
            </ul>
        </div>
    </div>
</body>
</html>";
?>
