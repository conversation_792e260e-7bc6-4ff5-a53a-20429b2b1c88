<?php

namespace Core;

/**
 * 缓存管理类
 * 支持文件缓存和内存缓存
 */
class Cache
{
    private static $instance = null;
    private $cacheDir;
    private $defaultTtl;
    private $memoryCache = [];
    
    private function __construct()
    {
        $this->cacheDir = DATA_PATH . '/cache';
        $this->defaultTtl = CACHE_TIME ?? 3600;
        
        // 创建缓存目录
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * 获取缓存实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 设置缓存
     */
    public function set($key, $value, $ttl = null)
    {
        if (!CACHE_ENABLED) {
            return false;
        }
        
        $ttl = $ttl ?? $this->defaultTtl;
        $expireTime = time() + $ttl;
        
        $cacheData = [
            'value' => $value,
            'expire_time' => $expireTime,
            'created_at' => time()
        ];
        
        // 内存缓存
        $this->memoryCache[$key] = $cacheData;
        
        // 文件缓存
        $filename = $this->getCacheFilename($key);
        return file_put_contents($filename, serialize($cacheData)) !== false;
    }
    
    /**
     * 获取缓存
     */
    public function get($key, $default = null)
    {
        if (!CACHE_ENABLED) {
            return $default;
        }
        
        // 先检查内存缓存
        if (isset($this->memoryCache[$key])) {
            $cacheData = $this->memoryCache[$key];
            if ($cacheData['expire_time'] > time()) {
                return $cacheData['value'];
            } else {
                unset($this->memoryCache[$key]);
            }
        }
        
        // 检查文件缓存
        $filename = $this->getCacheFilename($key);
        if (file_exists($filename)) {
            $cacheData = unserialize(file_get_contents($filename));
            
            if ($cacheData && $cacheData['expire_time'] > time()) {
                // 加载到内存缓存
                $this->memoryCache[$key] = $cacheData;
                return $cacheData['value'];
            } else {
                // 缓存过期，删除文件
                unlink($filename);
            }
        }
        
        return $default;
    }
    
    /**
     * 删除缓存
     */
    public function delete($key)
    {
        // 删除内存缓存
        unset($this->memoryCache[$key]);
        
        // 删除文件缓存
        $filename = $this->getCacheFilename($key);
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * 清空所有缓存
     */
    public function clear()
    {
        // 清空内存缓存
        $this->memoryCache = [];
        
        // 清空文件缓存
        $files = glob($this->cacheDir . '/*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
        
        return true;
    }
    
    /**
     * 检查缓存是否存在且未过期
     */
    public function has($key)
    {
        return $this->get($key) !== null;
    }
    
    /**
     * 获取或设置缓存（如果不存在则执行回调函数）
     */
    public function remember($key, $callback, $ttl = null)
    {
        $value = $this->get($key);
        
        if ($value === null) {
            $value = call_user_func($callback);
            $this->set($key, $value, $ttl);
        }
        
        return $value;
    }
    
    /**
     * 获取缓存统计信息
     */
    public function getStats()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $validCount = 0;
        $expiredCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $cacheData = unserialize(file_get_contents($file));
            
            if ($cacheData && $cacheData['expire_time'] > time()) {
                $validCount++;
            } else {
                $expiredCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_count' => $validCount,
            'expired_count' => $expiredCount,
            'memory_count' => count($this->memoryCache),
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir
        ];
    }
    
    /**
     * 清理过期缓存
     */
    public function cleanup()
    {
        $files = glob($this->cacheDir . '/*.cache');
        $cleanedCount = 0;
        
        foreach ($files as $file) {
            $cacheData = unserialize(file_get_contents($file));
            
            if (!$cacheData || $cacheData['expire_time'] <= time()) {
                unlink($file);
                $cleanedCount++;
            }
        }
        
        return $cleanedCount;
    }
    
    /**
     * 生成缓存文件名
     */
    private function getCacheFilename($key)
    {
        $hash = md5($key);
        return $this->cacheDir . '/' . $hash . '.cache';
    }
    
    /**
     * 生成缓存键
     */
    public static function key(...$parts)
    {
        return implode(':', $parts);
    }
    
    /**
     * 页面缓存
     */
    public function cachePage($key, $content, $ttl = null)
    {
        return $this->set('page:' . $key, $content, $ttl);
    }
    
    /**
     * 获取页面缓存
     */
    public function getPageCache($key)
    {
        return $this->get('page:' . $key);
    }
    
    /**
     * 数据库查询缓存
     */
    public function cacheQuery($sql, $params, $result, $ttl = null)
    {
        $key = 'query:' . md5($sql . serialize($params));
        return $this->set($key, $result, $ttl);
    }
    
    /**
     * 获取查询缓存
     */
    public function getQueryCache($sql, $params)
    {
        $key = 'query:' . md5($sql . serialize($params));
        return $this->get($key);
    }
}
