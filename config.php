<?php
/**
 * 系统配置文件
 */

// 定义路径常量（如果还没有定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', __DIR__);
}
if (!defined('APP_PATH')) {
    define('APP_PATH', ROOT_PATH . '/app');
}
if (!defined('TEMPLATE_PATH')) {
    define('TEMPLATE_PATH', ROOT_PATH . '/templates');
}
if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', ROOT_PATH . '/public');
}
if (!defined('DATA_PATH')) {
    define('DATA_PATH', ROOT_PATH . '/data');
}

// 数据库配置
define('DB_TYPE', 'sqlite'); // mysql 或 sqlite
define('DB_HOST', 'localhost');
define('DB_NAME', 'navstack');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

// SQLite数据库文件路径
define('SQLITE_PATH', DATA_PATH . '/navstack.db');

// 系统配置
define('SITE_NAME', 'NavStack 导航');
define('SITE_DESCRIPTION', '简洁高效的网址导航管理系统');
define('ADMIN_EMAIL', '<EMAIL>');

// 安全配置
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'admin123'); // 实际使用时应该加密存储

// 分页配置
define('PAGE_SIZE', 20);

// 上传配置
define('UPLOAD_PATH', DATA_PATH . '/uploads');
define('MAX_UPLOAD_SIZE', 2 * 1024 * 1024); // 2MB

// 缓存配置
define('CACHE_ENABLED', true);
define('CACHE_TIME', 3600); // 1小时

// 主题配置
define('DEFAULT_THEME', 'default');
define('ENABLE_DARK_MODE', true);

// 时区设置
date_default_timezone_set('Asia/Shanghai');

// 创建必要的目录
$dirs = [DATA_PATH, UPLOAD_PATH];
foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}
