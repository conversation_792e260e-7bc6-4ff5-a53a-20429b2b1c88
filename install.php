<?php
/**
 * TwoNav高级版 - 安装程序
 */

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('ROOT_PATH', __DIR__);
define('APP_PATH', ROOT_PATH . '/app');
define('TEMPLATE_PATH', ROOT_PATH . '/templates');
define('STATIC_PATH', ROOT_PATH . '/static');
define('DATA_PATH', ROOT_PATH . '/data');

// 自动加载
spl_autoload_register(function ($class) {
    $file = APP_PATH . '/' . str_replace('\\', '/', $class) . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

// 引入配置文件
require_once ROOT_PATH . '/config.php';

$message = '';
$error = '';

// 处理安装请求
if ($_POST && isset($_POST['install'])) {
    try {
        // 创建必要的目录
        $dirs = [DATA_PATH, DATA_PATH . '/uploads'];
        foreach ($dirs as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }

        // 执行安装
        $installer = new Core\Installer();
        $installer->install();
        
        $message = '安装成功！您现在可以访问系统了。';
    } catch (Exception $e) {
        $error = '安装失败: ' . $e->getMessage();
    }
}

// 检查系统要求
function checkRequirements() {
    $requirements = [
        'PHP版本 >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'SQLite扩展' => extension_loaded('sqlite3'),
        'PDO扩展' => extension_loaded('pdo'),
        'JSON扩展' => extension_loaded('json'),
        '数据目录可写' => is_writable(dirname(DATA_PATH))
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$canInstall = !in_array(false, $requirements, true);
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TwoNav高级版 - 安装程序</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .install-container {
            max-width: 600px;
            margin: 50px auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .requirement-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .requirement-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="install-container">
            <div class="text-center mb-4">
                <h1 class="text-primary">
                    <i class="fas fa-compass"></i>
                    TwoNav高级版
                </h1>
                <p class="text-muted">安装程序</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                    <hr>
                    <p class="mb-0">
                        <strong>默认管理员账号：</strong><br>
                        用户名：admin<br>
                        密码：admin123<br>
                        <a href="index.php" class="btn btn-primary mt-2">
                            <i class="fas fa-home"></i>
                            访问首页
                        </a>
                        <a href="index.php?c=admin" class="btn btn-outline-primary mt-2">
                            <i class="fas fa-cog"></i>
                            管理后台
                        </a>
                    </p>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if (!$message): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle"></i>
                            系统要求检查
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php foreach ($requirements as $requirement => $status): ?>
                            <div class="requirement-item">
                                <span><?php echo $requirement; ?></span>
                                <span class="<?php echo $status ? 'status-ok' : 'status-error'; ?>">
                                    <i class="fas fa-<?php echo $status ? 'check' : 'times'; ?>"></i>
                                    <?php echo $status ? '通过' : '失败'; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-info-circle"></i>
                            安装说明
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>系统将自动创建SQLite数据库</li>
                            <li><i class="fas fa-check text-success me-2"></i>创建默认管理员账号</li>
                            <li><i class="fas fa-check text-success me-2"></i>初始化系统数据和配置</li>
                            <li><i class="fas fa-check text-success me-2"></i>安装默认主题和示例数据</li>
                        </ul>
                    </div>
                </div>

                <?php if ($canInstall): ?>
                    <form method="post">
                        <div class="d-grid">
                            <button type="submit" name="install" class="btn btn-primary btn-lg">
                                <i class="fas fa-rocket"></i>
                                开始安装
                            </button>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        请先解决上述系统要求问题后再进行安装。
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="text-center mt-4">
                <small class="text-muted">
                    TwoNav高级版 - 开源导航系统
                </small>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
