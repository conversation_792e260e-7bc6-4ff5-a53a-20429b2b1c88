<?php

namespace Controllers;

use Core\Controller;
use Core\Cache;
use Models\Category;
use Models\Link;
use Models\User;

/**
 * API控制器
 * 提供RESTful API接口
 */
class ApiController extends Controller
{
    private $categoryModel;
    private $linkModel;
    private $userModel;
    private $cache;
    
    public function __construct()
    {
        parent::__construct();
        $this->categoryModel = new Category();
        $this->linkModel = new Link();
        $this->userModel = new User();
        $this->cache = Cache::getInstance();
        
        // 设置JSON响应头
        header('Content-Type: application/json; charset=utf-8');
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');
        
        // 处理OPTIONS请求
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit;
        }
    }
    
    /**
     * API首页
     */
    public function index()
    {
        $this->jsonResponse([
            'success' => true,
            'message' => 'TwoNav API v1.0',
            'endpoints' => [
                'GET /api/categories' => '获取分类列表',
                'GET /api/links' => '获取链接列表',
                'GET /api/links/{id}' => '获取单个链接',
                'GET /api/search' => '搜索链接',
                'GET /api/stats' => '获取统计信息',
                'POST /api/click/{id}' => '记录链接点击'
            ]
        ]);
    }
    
    /**
     * 获取分类列表
     */
    public function categories()
    {
        try {
            $cacheKey = 'api:categories';
            $categories = $this->cache->remember($cacheKey, function() {
                return $this->categoryModel->findAll('status = 1', [], 'sort_order ASC, id ASC');
            }, 1800);
            
            $this->jsonResponse([
                'success' => true,
                'data' => $categories,
                'total' => count($categories)
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '获取分类失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取链接列表
     */
    public function links()
    {
        try {
            $categoryId = $this->getGet('category_id');
            $page = max(1, (int)$this->getGet('page', 1));
            $limit = min(100, max(1, (int)$this->getGet('limit', 20)));
            
            $cacheKey = "api:links:{$categoryId}:{$page}:{$limit}";
            $result = $this->cache->remember($cacheKey, function() use ($categoryId, $page, $limit) {
                $where = 'status = 1';
                $params = [];
                
                if ($categoryId) {
                    $where .= ' AND category_id = :category_id';
                    $params['category_id'] = $categoryId;
                }
                
                return $this->linkModel->paginate($page, $limit, $where, $params, 'sort_order ASC, id DESC');
            }, 900);
            
            $this->jsonResponse([
                'success' => true,
                'data' => $result['data'],
                'pagination' => [
                    'page' => $result['page'],
                    'per_page' => $result['per_page'],
                    'total' => $result['total'],
                    'total_pages' => $result['total_pages']
                ]
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '获取链接失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取单个链接
     */
    public function link($id = null)
    {
        if (!$id) {
            $this->jsonResponse([
                'success' => false,
                'message' => '链接ID不能为空'
            ], 400);
            return;
        }
        
        try {
            $cacheKey = "api:link:{$id}";
            $link = $this->cache->remember($cacheKey, function() use ($id) {
                return $this->linkModel->findById($id);
            }, 1800);
            
            if (!$link || $link['status'] != 1) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => '链接不存在'
                ], 404);
                return;
            }
            
            $this->jsonResponse([
                'success' => true,
                'data' => $link
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '获取链接失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 搜索链接
     */
    public function search()
    {
        $keyword = trim($this->getGet('q', ''));
        
        if (empty($keyword)) {
            $this->jsonResponse([
                'success' => false,
                'message' => '搜索关键词不能为空'
            ], 400);
            return;
        }
        
        try {
            $cacheKey = "api:search:" . md5($keyword);
            $results = $this->cache->remember($cacheKey, function() use ($keyword) {
                return $this->linkModel->searchLinks($keyword);
            }, 600);
            
            $this->jsonResponse([
                'success' => true,
                'data' => $results,
                'total' => count($results),
                'keyword' => $keyword
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '搜索失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 获取统计信息
     */
    public function stats()
    {
        try {
            $cacheKey = 'api:stats';
            $stats = $this->cache->remember($cacheKey, function() {
                $db = \Core\Database::getInstance();
                
                return [
                    'categories' => $db->fetch("SELECT COUNT(*) as count FROM categories WHERE status = 1")['count'],
                    'links' => $db->fetch("SELECT COUNT(*) as count FROM links WHERE status = 1")['count'],
                    'total_clicks' => $db->fetch("SELECT SUM(click_count) as total FROM links WHERE status = 1")['total'] ?? 0,
                    'users' => $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 1")['count']
                ];
            }, 1800);
            
            $this->jsonResponse([
                'success' => true,
                'data' => $stats
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '获取统计失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 记录链接点击
     */
    public function click($id = null)
    {
        if (!$id) {
            $this->jsonResponse([
                'success' => false,
                'message' => '链接ID不能为空'
            ], 400);
            return;
        }
        
        try {
            // 检查链接是否存在
            $link = $this->linkModel->findById($id);
            if (!$link || $link['status'] != 1) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => '链接不存在'
                ], 404);
                return;
            }
            
            // 更新点击次数
            $this->linkModel->update($id, [
                'click_count' => $link['click_count'] + 1
            ]);
            
            // 记录点击统计
            $this->recordClickStats($id);
            
            // 清除相关缓存
            $this->cache->delete("api:link:{$id}");
            $this->cache->delete('api:stats');
            
            $this->jsonResponse([
                'success' => true,
                'message' => '点击记录成功',
                'url' => $link['url']
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '记录点击失败：' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * 记录点击统计
     */
    private function recordClickStats($linkId)
    {
        $db = \Core\Database::getInstance();
        
        $data = [
            'link_id' => $linkId,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'click_time' => date('Y-m-d H:i:s')
        ];
        
        $db->insert('click_stats', $data);
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        exit;
    }
}
