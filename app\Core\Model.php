<?php

namespace Core;

/**
 * 模型基类
 */
class Model
{
    protected $db;
    protected $table;
    protected $cache;
    protected $cacheEnabled = true;
    protected $defaultCacheTtl = 3600;

    public function __construct()
    {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();
    }
    
    /**
     * 查找所有记录
     */
    public function findAll($where = '', $params = [], $orderBy = '', $limit = '')
    {
        // 生成缓存键
        $cacheKey = $this->generateCacheKey('findAll', $where, $params, $orderBy, $limit);

        // 尝试从缓存获取
        if ($this->cacheEnabled) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        $sql = "SELECT * FROM {$this->table}";

        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }

        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }

        if (!empty($limit)) {
            $sql .= " LIMIT {$limit}";
        }

        $result = $this->db->fetchAll($sql, $params);

        // 缓存结果
        if ($this->cacheEnabled && $result !== false) {
            $this->cache->set($cacheKey, $result, $this->defaultCacheTtl);
        }

        return $result;
    }
    
    /**
     * 根据ID查找记录
     */
    public function findById($id)
    {
        // 生成缓存键
        $cacheKey = $this->generateCacheKey('findById', $id);

        // 尝试从缓存获取
        if ($this->cacheEnabled) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }

        $sql = "SELECT * FROM {$this->table} WHERE id = :id";
        $result = $this->db->fetch($sql, ['id' => $id]);

        // 缓存结果
        if ($this->cacheEnabled && $result !== false) {
            $this->cache->set($cacheKey, $result, $this->defaultCacheTtl);
        }

        return $result;
    }
    
    /**
     * 查找单条记录
     */
    public function findOne($where = '', $params = [])
    {
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $sql .= " LIMIT 1";
        
        return $this->db->fetch($sql, $params);
    }
    
    /**
     * 创建记录
     */
    public function create($data)
    {
        // 添加时间戳
        if (!isset($data['created_at'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }

        $result = $this->db->insert($this->table, $data);

        // 清除相关缓存
        if ($result && $this->cacheEnabled) {
            $this->clearModelCache();
        }

        return $result;
    }
    
    /**
     * 更新记录
     */
    public function update($id, $data)
    {
        // 添加更新时间戳
        $data['updated_at'] = date('Y-m-d H:i:s');

        $result = $this->db->update($this->table, $data, 'id = :id', ['id' => $id]);

        // 清除相关缓存
        if ($result && $this->cacheEnabled) {
            $this->clearModelCache();
        }

        return $result;
    }

    /**
     * 删除记录
     */
    public function delete($id)
    {
        $result = $this->db->delete($this->table, 'id = :id', ['id' => $id]);

        // 清除相关缓存
        if ($result && $this->cacheEnabled) {
            $this->clearModelCache();
        }

        return $result;
    }
    
    /**
     * 统计记录数
     */
    public function count($where = '', $params = [])
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        $result = $this->db->fetch($sql, $params);
        return $result['count'] ?? 0;
    }
    
    /**
     * 分页查询
     */
    public function paginate($page = 1, $perPage = 20, $where = '', $params = [], $orderBy = '')
    {
        $offset = ($page - 1) * $perPage;
        
        // 获取总数
        $total = $this->count($where, $params);
        
        // 获取数据
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($where)) {
            $sql .= " WHERE {$where}";
        }
        
        if (!empty($orderBy)) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $data = $this->db->fetchAll($sql, $params);
        
        return [
            'data' => $data,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }

    /**
     * 生成缓存键
     */
    protected function generateCacheKey(...$parts)
    {
        $key = $this->table . ':' . implode(':', array_map(function($part) {
            return is_array($part) ? md5(serialize($part)) : (string)$part;
        }, $parts));

        return md5($key);
    }

    /**
     * 清除模型相关缓存
     */
    public function clearCache()
    {
        // 这里可以实现更精确的缓存清理
        // 暂时清空所有缓存
        $this->cache->clear();
    }

    /**
     * 清除当前模型相关的缓存
     */
    protected function clearModelCache()
    {
        // 简单实现：清空所有缓存
        // 在实际应用中，可以实现更精确的缓存清理策略
        $this->cache->clear();
    }

    /**
     * 设置缓存状态
     */
    public function setCacheEnabled($enabled)
    {
        $this->cacheEnabled = $enabled;
        return $this;
    }

    /**
     * 设置缓存TTL
     */
    public function setCacheTtl($ttl)
    {
        $this->defaultCacheTtl = $ttl;
        return $this;
    }
}
