<?php

namespace Core;

/**
 * SEO优化类
 * 提供搜索引擎优化功能
 */
class SEO
{
    private static $instance = null;
    private $metaTags = [];
    private $structuredData = [];
    
    private function __construct()
    {
        // 设置默认meta标签
        $this->setDefaultMeta();
    }
    
    /**
     * 获取SEO实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 设置默认meta标签
     */
    private function setDefaultMeta()
    {
        $this->metaTags = [
            'title' => SITE_NAME,
            'description' => SITE_DESCRIPTION,
            'keywords' => '导航,网址,书签,工具',
            'author' => 'TwoNav',
            'robots' => 'index,follow',
            'viewport' => 'width=device-width, initial-scale=1.0',
            'charset' => 'UTF-8'
        ];
    }
    
    /**
     * 设置页面标题
     */
    public function setTitle($title)
    {
        $this->metaTags['title'] = $title;
        return $this;
    }
    
    /**
     * 设置页面描述
     */
    public function setDescription($description)
    {
        $this->metaTags['description'] = $description;
        return $this;
    }
    
    /**
     * 设置关键词
     */
    public function setKeywords($keywords)
    {
        if (is_array($keywords)) {
            $keywords = implode(',', $keywords);
        }
        $this->metaTags['keywords'] = $keywords;
        return $this;
    }
    
    /**
     * 设置作者
     */
    public function setAuthor($author)
    {
        $this->metaTags['author'] = $author;
        return $this;
    }
    
    /**
     * 设置robots
     */
    public function setRobots($robots)
    {
        $this->metaTags['robots'] = $robots;
        return $this;
    }
    
    /**
     * 设置自定义meta标签
     */
    public function setMeta($name, $content)
    {
        $this->metaTags[$name] = $content;
        return $this;
    }
    
    /**
     * 获取meta标签
     */
    public function getMeta($name = null)
    {
        if ($name === null) {
            return $this->metaTags;
        }
        return $this->metaTags[$name] ?? null;
    }
    
    /**
     * 生成meta标签HTML
     */
    public function renderMetaTags()
    {
        $html = '';
        
        // 字符集
        if (isset($this->metaTags['charset'])) {
            $html .= '<meta charset="' . htmlspecialchars($this->metaTags['charset']) . '">' . "\n";
        }
        
        // viewport
        if (isset($this->metaTags['viewport'])) {
            $html .= '<meta name="viewport" content="' . htmlspecialchars($this->metaTags['viewport']) . '">' . "\n";
        }
        
        // 标题
        if (isset($this->metaTags['title'])) {
            $html .= '<title>' . htmlspecialchars($this->metaTags['title']) . '</title>' . "\n";
        }
        
        // 其他meta标签
        foreach ($this->metaTags as $name => $content) {
            if (in_array($name, ['charset', 'viewport', 'title'])) {
                continue;
            }
            $html .= '<meta name="' . htmlspecialchars($name) . '" content="' . htmlspecialchars($content) . '">' . "\n";
        }
        
        // Open Graph标签
        $html .= $this->renderOpenGraphTags();
        
        // Twitter Card标签
        $html .= $this->renderTwitterCardTags();
        
        return $html;
    }
    
    /**
     * 生成Open Graph标签
     */
    private function renderOpenGraphTags()
    {
        $html = '';
        $ogTags = [
            'og:title' => $this->metaTags['title'] ?? '',
            'og:description' => $this->metaTags['description'] ?? '',
            'og:type' => 'website',
            'og:url' => $this->getCurrentUrl(),
            'og:site_name' => SITE_NAME
        ];
        
        foreach ($ogTags as $property => $content) {
            if (!empty($content)) {
                $html .= '<meta property="' . htmlspecialchars($property) . '" content="' . htmlspecialchars($content) . '">' . "\n";
            }
        }
        
        return $html;
    }
    
    /**
     * 生成Twitter Card标签
     */
    private function renderTwitterCardTags()
    {
        $html = '';
        $twitterTags = [
            'twitter:card' => 'summary',
            'twitter:title' => $this->metaTags['title'] ?? '',
            'twitter:description' => $this->metaTags['description'] ?? ''
        ];
        
        foreach ($twitterTags as $name => $content) {
            if (!empty($content)) {
                $html .= '<meta name="' . htmlspecialchars($name) . '" content="' . htmlspecialchars($content) . '">' . "\n";
            }
        }
        
        return $html;
    }
    
    /**
     * 添加结构化数据
     */
    public function addStructuredData($type, $data)
    {
        $this->structuredData[] = [
            '@context' => 'https://schema.org',
            '@type' => $type,
            ...$data
        ];
        return $this;
    }
    
    /**
     * 生成结构化数据JSON-LD
     */
    public function renderStructuredData()
    {
        if (empty($this->structuredData)) {
            return '';
        }
        
        $html = '<script type="application/ld+json">' . "\n";
        $html .= json_encode($this->structuredData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        $html .= "\n" . '</script>' . "\n";
        
        return $html;
    }
    
    /**
     * 生成网站结构化数据
     */
    public function addWebsiteStructuredData()
    {
        $this->addStructuredData('WebSite', [
            'name' => SITE_NAME,
            'description' => SITE_DESCRIPTION,
            'url' => $this->getBaseUrl(),
            'potentialAction' => [
                '@type' => 'SearchAction',
                'target' => $this->getBaseUrl() . '/index.php?c=home&action=search&q={search_term_string}',
                'query-input' => 'required name=search_term_string'
            ]
        ]);
        return $this;
    }
    
    /**
     * 生成导航链接结构化数据
     */
    public function addBreadcrumbStructuredData($items)
    {
        $listItems = [];
        foreach ($items as $index => $item) {
            $listItems[] = [
                '@type' => 'ListItem',
                'position' => $index + 1,
                'name' => $item['name'],
                'item' => $item['url'] ?? null
            ];
        }
        
        $this->addStructuredData('BreadcrumbList', [
            'itemListElement' => $listItems
        ]);
        return $this;
    }
    
    /**
     * 生成站点地图
     */
    public function generateSitemap()
    {
        $db = Database::getInstance();
        $baseUrl = $this->getBaseUrl();
        
        $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        // 首页
        $xml .= $this->addSitemapUrl($baseUrl, date('Y-m-d'), 'daily', '1.0');
        
        // 分类页面
        $categories = $db->fetchAll("SELECT id, updated_at FROM categories WHERE status = 1");
        foreach ($categories as $category) {
            $url = $baseUrl . '/index.php?c=home&action=category&id=' . $category['id'];
            $lastmod = date('Y-m-d', strtotime($category['updated_at']));
            $xml .= $this->addSitemapUrl($url, $lastmod, 'weekly', '0.8');
        }
        
        // 链接页面（如果有详情页）
        $links = $db->fetchAll("SELECT id, updated_at FROM links WHERE status = 1 LIMIT 1000");
        foreach ($links as $link) {
            $url = $baseUrl . '/index.php?c=link&id=' . $link['id'];
            $lastmod = date('Y-m-d', strtotime($link['updated_at']));
            $xml .= $this->addSitemapUrl($url, $lastmod, 'monthly', '0.6');
        }
        
        $xml .= '</urlset>';
        
        return $xml;
    }
    
    /**
     * 添加站点地图URL
     */
    private function addSitemapUrl($url, $lastmod, $changefreq, $priority)
    {
        $xml = '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url) . '</loc>' . "\n";
        $xml .= '    <lastmod>' . $lastmod . '</lastmod>' . "\n";
        $xml .= '    <changefreq>' . $changefreq . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $priority . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";
        
        return $xml;
    }
    
    /**
     * 获取当前URL
     */
    private function getCurrentUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        
        return $protocol . '://' . $host . $uri;
    }
    
    /**
     * 获取基础URL
     */
    private function getBaseUrl()
    {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        
        return $protocol . '://' . $host;
    }
}
