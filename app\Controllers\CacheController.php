<?php

namespace Controllers;

use Core\Controller;
use Core\Cache;

/**
 * 缓存管理控制器
 */
class CacheController extends Controller
{
    private $cache;
    
    public function __construct()
    {
        parent::__construct();
        $this->cache = Cache::getInstance();
        
        // 检查管理员权限
        $this->checkAdminAuth();
    }
    
    /**
     * 缓存管理首页
     */
    public function index()
    {
        $stats = $this->cache->getStats();
        
        $data = [
            'title' => '缓存管理 - ' . SITE_NAME,
            'stats' => $stats,
            'cache_enabled' => CACHE_ENABLED
        ];
        
        $this->render('admin/cache/index', $data);
    }
    
    /**
     * 清空所有缓存
     */
    public function clear()
    {
        try {
            $this->cache->clear();
            $this->jsonResponse([
                'success' => true,
                'message' => '缓存清空成功'
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '缓存清空失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 清理过期缓存
     */
    public function cleanup()
    {
        try {
            $cleanedCount = $this->cache->cleanup();
            $this->jsonResponse([
                'success' => true,
                'message' => "清理了 {$cleanedCount} 个过期缓存文件"
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '清理失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public function stats()
    {
        $stats = $this->cache->getStats();
        $this->jsonResponse([
            'success' => true,
            'data' => $stats
        ]);
    }
    
    /**
     * 预热缓存
     */
    public function warmup()
    {
        try {
            // 预热常用数据
            $this->warmupCommonData();
            
            $this->jsonResponse([
                'success' => true,
                'message' => '缓存预热完成'
            ]);
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => '缓存预热失败：' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * 预热常用数据
     */
    private function warmupCommonData()
    {
        // 预热分类数据
        $categoryModel = new \Models\Category();
        $categoryModel->findAll('status = 1', [], 'sort_order ASC');
        
        // 预热热门链接
        $linkModel = new \Models\Link();
        $linkModel->findAll('status = 1', [], 'click_count DESC', '20');
        
        // 预热最新链接
        $linkModel->findAll('status = 1', [], 'created_at DESC', '20');
        
        // 预热统计数据
        $this->warmupStats();
    }
    
    /**
     * 预热统计数据
     */
    private function warmupStats()
    {
        $db = \Core\Database::getInstance();
        
        // 缓存分类数量
        $categoryCount = $db->fetch("SELECT COUNT(*) as count FROM categories WHERE status = 1");
        $this->cache->set('stats:category_count', $categoryCount['count'], 3600);
        
        // 缓存链接数量
        $linkCount = $db->fetch("SELECT COUNT(*) as count FROM links WHERE status = 1");
        $this->cache->set('stats:link_count', $linkCount['count'], 3600);
        
        // 缓存本月访问量（模拟数据）
        $monthlyViews = rand(1000, 5000);
        $this->cache->set('stats:monthly_views', $monthlyViews, 3600);
        
        // 缓存本月点击量（模拟数据）
        $monthlyClicks = rand(500, 2000);
        $this->cache->set('stats:monthly_clicks', $monthlyClicks, 3600);
    }
    
    /**
     * 检查管理员权限
     */
    private function checkAdminAuth()
    {
        session_start();
        if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
            $this->jsonResponse([
                'success' => false,
                'message' => '需要管理员权限'
            ], 401);
            exit;
        }
    }
    
    /**
     * JSON响应
     */
    private function jsonResponse($data, $statusCode = 200)
    {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
}
