<?php

namespace Core;

/**
 * 应用程序主类
 */
class Application
{
    private $router;
    private $database;
    
    public function __construct()
    {
        $this->router = new Router();
        $this->database = Database::getInstance();
        
        // 初始化数据库
        $this->initDatabase();
        
        // 注册路由
        $this->registerRoutes();
    }
    
    /**
     * 运行应用
     */
    public function run()
    {
        $this->router->dispatch();
    }
    
    /**
     * 初始化数据库
     */
    private function initDatabase()
    {
        if (!$this->database->isInstalled()) {
            $installer = new Installer();
            $installer->install();
        }
    }
    
    /**
     * 注册路由
     */
    private function registerRoutes()
    {
        // 默认路由
        $this->router->addRoute('', 'Controllers\HomeController', 'index');

        // 首页路由
        $this->router->addRoute('home', 'Controllers\HomeController', 'index');
        $this->router->addRoute('search', 'Controllers\HomeController', 'search');

        // 链接路由
        $this->router->addRoute('click', 'Controllers\LinkController', 'click');
        $this->router->addRoute('link', 'Controllers\LinkController', 'index');

        // 管理员路由
        $this->router->addRoute('admin', 'Controllers\AdminController', 'index');
        $this->router->addRoute('login', 'Controllers\AdminController', 'login');
        $this->router->addRoute('logout', 'Controllers\AdminController', 'logout');

        // 分类管理
        $this->router->addRoute('category', 'Controllers\CategoryController', 'index');

        // 主题管理
        $this->router->addRoute('theme', 'Controllers\ThemeController', 'index');

        // 文章管理
        $this->router->addRoute('article', 'Controllers\ArticleController', 'index');

        // 用户管理
        $this->router->addRoute('user', 'Controllers\UserController', 'index');

        // 扩展功能
        $this->router->addRoute('guestbook', 'Controllers\GuestbookController', 'index');
        $this->router->addRoute('submission', 'Controllers\SubmissionController', 'index');
        $this->router->addRoute('advertisement', 'Controllers\AdvertisementController', 'index');

        // API路由
        $this->router->addRoute('api', 'Controllers\ApiController', 'index');

        // 缓存管理
        $this->router->addRoute('cache', 'Controllers\CacheController', 'index');
    }
}
