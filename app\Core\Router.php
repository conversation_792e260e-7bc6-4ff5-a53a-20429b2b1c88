<?php

namespace Core;

/**
 * 路由器类
 */
class Router
{
    private $routes = [];
    
    /**
     * 添加路由
     */
    public function addRoute($pattern, $controller, $action)
    {
        $this->routes[$pattern] = [
            'controller' => $controller,
            'action' => $action
        ];
    }
    
    /**
     * 分发请求
     */
    public function dispatch()
    {
        // 获取路由参数
        $controller = $_GET['c'] ?? '';
        $action = $_GET['action'] ?? 'index';
        $id = $_GET['id'] ?? null;

        // 特殊处理API路由
        if ($controller === 'api') {
            $this->dispatchApi();
            return;
        }

        // 查找路由
        if (isset($this->routes[$controller])) {
            $route = $this->routes[$controller];
            $controllerClass = $route['controller'];
            $actionMethod = $action;
        } else {
            // 默认路由
            $controllerClass = 'Controllers\HomeController';
            $actionMethod = 'index';
        }

        // 实例化控制器
        if (class_exists($controllerClass)) {
            $controllerInstance = new $controllerClass();

            // 检查方法是否存在
            if (method_exists($controllerInstance, $actionMethod)) {
                // 调用方法
                if ($id !== null) {
                    $controllerInstance->$actionMethod($id);
                } else {
                    $controllerInstance->$actionMethod();
                }
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }
    
    /**
     * 分发API请求
     */
    private function dispatchApi()
    {
        $path = $_GET['path'] ?? '';
        $method = $_SERVER['REQUEST_METHOD'];
        $id = $_GET['id'] ?? null;

        $controllerInstance = new \Controllers\ApiController();

        // 根据路径分发到不同的方法
        switch ($path) {
            case 'categories':
                $controllerInstance->categories();
                break;

            case 'links':
                if ($id) {
                    $controllerInstance->link($id);
                } else {
                    $controllerInstance->links();
                }
                break;

            case 'search':
                $controllerInstance->search();
                break;

            case 'stats':
                $controllerInstance->stats();
                break;

            case 'click':
                if ($method === 'POST' && $id) {
                    $controllerInstance->click($id);
                } else {
                    $this->apiError('Invalid request', 400);
                }
                break;

            default:
                $controllerInstance->index();
                break;
        }
    }

    /**
     * API错误响应
     */
    private function apiError($message, $code = 400)
    {
        http_response_code($code);
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }

    /**
     * 显示404页面
     */
    private function show404()
    {
        http_response_code(404);
        echo "404 - Page Not Found";
    }

    /**
     * 生成URL
     */
    public static function url($controller = '', $action = 'index', $params = [])
    {
        $url = 'index.php';
        $query = [];

        if (!empty($controller)) {
            $query['c'] = $controller;
        }

        if ($action !== 'index') {
            $query['action'] = $action;
        }

        $query = array_merge($query, $params);

        if (!empty($query)) {
            $url .= '?' . http_build_query($query);
        }

        return $url;
    }
}
