<?php

namespace Core;

/**
 * 统计分析类
 * 提供高级统计和数据分析功能
 */
class Statistics
{
    private static $instance = null;
    private $db;
    private $cache;
    
    private function __construct()
    {
        $this->db = Database::getInstance();
        $this->cache = Cache::getInstance();
    }
    
    /**
     * 获取统计实例
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 获取基础统计数据
     */
    public function getBasicStats()
    {
        return $this->cache->remember('stats:basic', function() {
            return [
                'categories' => $this->db->fetch("SELECT COUNT(*) as count FROM categories WHERE status = 1")['count'],
                'links' => $this->db->fetch("SELECT COUNT(*) as count FROM links WHERE status = 1")['count'],
                'users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 1")['count'],
                'total_clicks' => $this->db->fetch("SELECT SUM(click_count) as total FROM links WHERE status = 1")['total'] ?? 0,
                'articles' => $this->db->fetch("SELECT COUNT(*) as count FROM articles WHERE status = 1")['count'],
                'guestbook' => $this->db->fetch("SELECT COUNT(*) as count FROM guestbook")['count']
            ];
        }, 1800);
    }
    
    /**
     * 获取访问统计
     */
    public function getVisitStats($days = 30)
    {
        $cacheKey = "stats:visits:{$days}";
        return $this->cache->remember($cacheKey, function() use ($days) {
            $startDate = date('Y-m-d', strtotime("-{$days} days"));
            
            // 模拟访问数据（实际应用中应该有访问记录表）
            $data = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $data[] = [
                    'date' => $date,
                    'visits' => rand(50, 200),
                    'unique_visitors' => rand(30, 150),
                    'page_views' => rand(100, 500)
                ];
            }
            
            return $data;
        }, 3600);
    }
    
    /**
     * 获取点击统计
     */
    public function getClickStats($days = 30)
    {
        $cacheKey = "stats:clicks:{$days}";
        return $this->cache->remember($cacheKey, function() use ($days) {
            $startDate = date('Y-m-d', strtotime("-{$days} days"));
            
            $sql = "
                SELECT 
                    DATE(click_time) as date,
                    COUNT(*) as clicks
                FROM click_stats 
                WHERE click_time >= :start_date
                GROUP BY DATE(click_time)
                ORDER BY date ASC
            ";
            
            $result = $this->db->fetchAll($sql, ['start_date' => $startDate]);
            
            // 填充缺失的日期
            $data = [];
            for ($i = $days - 1; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $clicks = 0;
                
                foreach ($result as $row) {
                    if ($row['date'] === $date) {
                        $clicks = $row['clicks'];
                        break;
                    }
                }
                
                $data[] = [
                    'date' => $date,
                    'clicks' => $clicks
                ];
            }
            
            return $data;
        }, 1800);
    }
    
    /**
     * 获取热门链接
     */
    public function getPopularLinks($limit = 10)
    {
        $cacheKey = "stats:popular_links:{$limit}";
        return $this->cache->remember($cacheKey, function() use ($limit) {
            $sql = "
                SELECT 
                    l.id,
                    l.title,
                    l.url,
                    l.click_count,
                    c.name as category_name
                FROM links l
                LEFT JOIN categories c ON l.category_id = c.id
                WHERE l.status = 1
                ORDER BY l.click_count DESC
                LIMIT :limit
            ";
            
            return $this->db->fetchAll($sql, ['limit' => $limit]);
        }, 1800);
    }
    
    /**
     * 获取分类统计
     */
    public function getCategoryStats()
    {
        return $this->cache->remember('stats:categories', function() {
            $sql = "
                SELECT 
                    c.id,
                    c.name,
                    COUNT(l.id) as link_count,
                    SUM(l.click_count) as total_clicks
                FROM categories c
                LEFT JOIN links l ON c.id = l.category_id AND l.status = 1
                WHERE c.status = 1
                GROUP BY c.id, c.name
                ORDER BY link_count DESC
            ";
            
            return $this->db->fetchAll($sql);
        }, 1800);
    }
    
    /**
     * 获取用户活动统计
     */
    public function getUserActivityStats()
    {
        return $this->cache->remember('stats:user_activity', function() {
            // 最近登录用户
            $recentUsers = $this->db->fetchAll("
                SELECT username, last_login_time 
                FROM users 
                WHERE last_login_time IS NOT NULL 
                ORDER BY last_login_time DESC 
                LIMIT 10
            ");
            
            // 用户注册趋势（最近30天）
            $registrationTrend = [];
            for ($i = 29; $i >= 0; $i--) {
                $date = date('Y-m-d', strtotime("-{$i} days"));
                $count = $this->db->fetch("
                    SELECT COUNT(*) as count 
                    FROM users 
                    WHERE DATE(created_at) = :date
                ", ['date' => $date])['count'];
                
                $registrationTrend[] = [
                    'date' => $date,
                    'registrations' => $count
                ];
            }
            
            return [
                'recent_users' => $recentUsers,
                'registration_trend' => $registrationTrend
            ];
        }, 1800);
    }
    
    /**
     * 获取搜索统计
     */
    public function getSearchStats()
    {
        return $this->cache->remember('stats:search', function() {
            // 模拟搜索数据（实际应用中应该记录搜索关键词）
            return [
                'total_searches' => rand(1000, 5000),
                'popular_keywords' => [
                    ['keyword' => 'GitHub', 'count' => rand(100, 500)],
                    ['keyword' => 'Vue.js', 'count' => rand(80, 400)],
                    ['keyword' => 'React', 'count' => rand(70, 350)],
                    ['keyword' => 'Node.js', 'count' => rand(60, 300)],
                    ['keyword' => 'Python', 'count' => rand(50, 250)]
                ]
            ];
        }, 3600);
    }
    
    /**
     * 获取系统性能统计
     */
    public function getPerformanceStats()
    {
        return $this->cache->remember('stats:performance', function() {
            $cacheStats = $this->cache->getStats();
            
            return [
                'cache' => $cacheStats,
                'database' => [
                    'total_queries' => rand(1000, 10000),
                    'avg_query_time' => round(rand(1, 50) / 10, 2) . 'ms',
                    'slow_queries' => rand(0, 10)
                ],
                'memory' => [
                    'usage' => round(memory_get_usage() / 1024 / 1024, 2) . 'MB',
                    'peak' => round(memory_get_peak_usage() / 1024 / 1024, 2) . 'MB'
                ],
                'disk' => [
                    'total' => $this->formatBytes(disk_total_space('.')),
                    'free' => $this->formatBytes(disk_free_space('.')),
                    'used' => $this->formatBytes(disk_total_space('.') - disk_free_space('.'))
                ]
            ];
        }, 300); // 5分钟缓存
    }
    
    /**
     * 生成统计报表
     */
    public function generateReport($type = 'daily', $date = null)
    {
        if (!$date) {
            $date = date('Y-m-d');
        }
        
        $cacheKey = "stats:report:{$type}:{$date}";
        return $this->cache->remember($cacheKey, function() use ($type, $date) {
            switch ($type) {
                case 'daily':
                    return $this->generateDailyReport($date);
                case 'weekly':
                    return $this->generateWeeklyReport($date);
                case 'monthly':
                    return $this->generateMonthlyReport($date);
                default:
                    return $this->generateDailyReport($date);
            }
        }, 3600);
    }
    
    /**
     * 生成日报
     */
    private function generateDailyReport($date)
    {
        return [
            'date' => $date,
            'type' => 'daily',
            'summary' => [
                'new_links' => $this->db->fetch("SELECT COUNT(*) as count FROM links WHERE DATE(created_at) = :date", ['date' => $date])['count'],
                'new_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = :date", ['date' => $date])['count'],
                'total_clicks' => $this->db->fetch("SELECT COUNT(*) as count FROM click_stats WHERE DATE(click_time) = :date", ['date' => $date])['count'],
                'page_views' => rand(500, 2000) // 模拟数据
            ],
            'top_links' => $this->getTopLinksForDate($date),
            'top_categories' => $this->getTopCategoriesForDate($date)
        ];
    }
    
    /**
     * 生成周报
     */
    private function generateWeeklyReport($date)
    {
        $startDate = date('Y-m-d', strtotime($date . ' -6 days'));
        
        return [
            'start_date' => $startDate,
            'end_date' => $date,
            'type' => 'weekly',
            'summary' => [
                'new_links' => $this->db->fetch("SELECT COUNT(*) as count FROM links WHERE created_at BETWEEN :start AND :end", ['start' => $startDate, 'end' => $date . ' 23:59:59'])['count'],
                'new_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE created_at BETWEEN :start AND :end", ['start' => $startDate, 'end' => $date . ' 23:59:59'])['count'],
                'total_clicks' => $this->db->fetch("SELECT COUNT(*) as count FROM click_stats WHERE click_time BETWEEN :start AND :end", ['start' => $startDate, 'end' => $date . ' 23:59:59'])['count'],
                'avg_daily_views' => rand(300, 1500) // 模拟数据
            ]
        ];
    }
    
    /**
     * 生成月报
     */
    private function generateMonthlyReport($date)
    {
        $startDate = date('Y-m-01', strtotime($date));
        $endDate = date('Y-m-t', strtotime($date));
        
        return [
            'start_date' => $startDate,
            'end_date' => $endDate,
            'type' => 'monthly',
            'summary' => [
                'new_links' => $this->db->fetch("SELECT COUNT(*) as count FROM links WHERE created_at BETWEEN :start AND :end", ['start' => $startDate, 'end' => $endDate . ' 23:59:59'])['count'],
                'new_users' => $this->db->fetch("SELECT COUNT(*) as count FROM users WHERE created_at BETWEEN :start AND :end", ['start' => $startDate, 'end' => $endDate . ' 23:59:59'])['count'],
                'total_clicks' => $this->db->fetch("SELECT COUNT(*) as count FROM click_stats WHERE click_time BETWEEN :start AND :end", ['start' => $startDate, 'end' => $endDate . ' 23:59:59'])['count'],
                'growth_rate' => rand(5, 25) . '%' // 模拟数据
            ]
        ];
    }
    
    /**
     * 获取指定日期的热门链接
     */
    private function getTopLinksForDate($date)
    {
        $sql = "
            SELECT l.title, l.url, COUNT(cs.id) as clicks
            FROM links l
            LEFT JOIN click_stats cs ON l.id = cs.link_id AND DATE(cs.click_time) = :date
            WHERE l.status = 1
            GROUP BY l.id
            ORDER BY clicks DESC
            LIMIT 5
        ";
        
        return $this->db->fetchAll($sql, ['date' => $date]);
    }
    
    /**
     * 获取指定日期的热门分类
     */
    private function getTopCategoriesForDate($date)
    {
        $sql = "
            SELECT c.name, COUNT(cs.id) as clicks
            FROM categories c
            LEFT JOIN links l ON c.id = l.category_id
            LEFT JOIN click_stats cs ON l.id = cs.link_id AND DATE(cs.click_time) = :date
            WHERE c.status = 1
            GROUP BY c.id
            ORDER BY clicks DESC
            LIMIT 5
        ";
        
        return $this->db->fetchAll($sql, ['date' => $date]);
    }
    
    /**
     * 格式化字节大小
     */
    private function formatBytes($size, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}
